p {
  font-size: 13px;
  color: #034e88;
  margin: 0;
}

.alert {
  background-color: #dbebfb;
}

.filters {
  position: relative;
  background: #dbebfb;
  border-radius: 8px;
  padding: 30px 45px;
  p {
    font-size: 13px;
    color: #034e88;
    margin: 0;
  }

  .row {
    margin-top: 20px;
  }
  button {
    background-color: #008bff;
    border: none;
    cursor: pointer;
    color: white;
    font-size: 0.9rem;
    align-items: center;
    border-radius: 0.25rem;
    padding: 10px;
    width: 50%;
  }
  button:disabled {
    background-color: #d6d6d6;
    color: #c0c0c0;
  }
  #settings {
    position: absolute;
    right: 50px;
    color: #044962;
    cursor: pointer;
  }
}

#tableHead {
  padding: 18px 44px 18px 45px;
  margin-top: 20px;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  background: white;
  border-bottom: 3px solid #e1e5f2;
  h4 {
    font-size: 25px;
    color: #044962;
    font-weight: 600;
    margin: 0;
  }
}

.table-container{
     width: 100%;
    overflow-x: auto;
}

table {
  width: 100%;
  border-radius: 8px;
  background: white;
  table-layout: fixed;
  width: 100%;
  min-width: 650px;
  tbody {
    padding: 100px;
  }
  tr:nth-of-type(odd) {
    background-color: #edf5fd;
  }
  td,
  th {
    padding: 15px 0px;
    padding-left: 40px;
  }
}

.mat-form-field {
  width: 100%;
}
::ng-deep {
  .date .mat-form-field-infix {
    display: flex !important;
    padding: 0 !important;
  }
  .mat-date-range-input {
    display: flex !important;
    align-items: center;
  }
}

@media only screen and (max-width: 1200px) {
  table{
    th{
      font-size: 15px !important;
    }
    td{
      font-size: 14px !important;
    }
  }
}

@media only screen and (max-width: 768px) {
  .filters {
    padding: 25px 35px;

    #settings{
      right: 35px;
    }
  }
 
}

@media only screen and (max-width: 576px) {
  .filters {
    padding: 20px 20px;

    #settings{
      right: 20px;
    }
  }

  
}
