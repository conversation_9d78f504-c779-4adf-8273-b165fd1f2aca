import { Component, EventEmitter, Input, OnInit, Output, OnDestroy } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { AVAILABLE_CURRENCIES, CURRENCY_ORDER, CURRENCY_TRANSLATIONS } from '../../constants/available_currencies.const';
import { ProductPrice } from '../../models/product.model';

interface CurrencyInfo {
  symbol: string;
  name: string;
  symbol_native: string;
  code: string;
  name_plural: string;
  translation_key?: string;
  [key: string]: any;
}

@Component({
  selector: 'app-currency-price-input',
  templateUrl: './currency-price-input.component.html',
  styleUrls: ['./currency-price-input.component.scss']
})
export class CurrencyPriceInputComponent implements OnInit, OnDestroy {
  @Input() existingPrices: { [currencyCode: string]: number } = {};
  @Output() pricesChanged = new EventEmitter<{ [currencyCode: string]: number }>();

  availableCurrencies: { [key: string]: CurrencyInfo } = AVAILABLE_CURRENCIES;
  currencyOrder = CURRENCY_ORDER;
  currencyTranslations: { [key: string]: string } = CURRENCY_TRANSLATIONS;
  currencyPrices: ProductPrice[] = [];
  unusedCurrencies: string[] = [];
  filteredCurrencies: string[] = [];
  selectedCurrencySymbol: string = '';

  // Individual form controls for each currency price
  priceControls: FormControl[] = [];
  currencyCodeControl: FormControl = new FormControl('');
  priceControl: FormControl = new FormControl('', [Validators.min(0)]);

  private destroy$ = new Subject<void>();

  constructor() {}

  ngOnInit(): void {
    this.loadExistingPrices();
    this.updateUnusedCurrencies();
    this.setupAutomaticSaving();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadExistingPrices(): void {
    this.currencyPrices = [];
    this.priceControls = [];
    if (this.existingPrices) {
      for (const [currencyCode, price] of Object.entries(this.existingPrices)) {
        if (this.currencyOrder.includes(currencyCode)) {
          this.currencyPrices.push({
            currencyCode,
            price
          });
          // Create a form control for each existing price
          const control = new FormControl(price, [Validators.min(0)]);
          this.priceControls.push(control);
        }
      }
    }
  }

  private updateUnusedCurrencies(): void {
    const usedCurrencies = this.currencyPrices.map(cp => cp.currencyCode);
    this.unusedCurrencies = this.currencyOrder
      .filter(code => !usedCurrencies.includes(code) && code !== 'RSD')
      .sort((a, b) => {
        const indexA = this.currencyOrder.indexOf(a);
        const indexB = this.currencyOrder.indexOf(b);
        return indexA - indexB;
      });

    this.filteredCurrencies = [...this.unusedCurrencies];

    // Clear the form when no unused currencies
    if (this.unusedCurrencies.length === 0) {
      this.currencyCodeControl.setValue('');
      this.priceControl.setValue('');
    }
  }

  private setupAutomaticSaving(): void {
    // Setup automatic saving for new currency/price inputs
    this.currencyCodeControl.valueChanges
      .pipe(takeUntil(this.destroy$), debounceTime(500))
      .subscribe(currencyCode => {
        this.selectedCurrencySymbol = currencyCode ? this.getCurrencySymbol(currencyCode) : '';
        this.tryAddCurrencyPrice();
      });

    this.priceControl.valueChanges
      .pipe(takeUntil(this.destroy$), debounceTime(500))
      .subscribe(() => {
        this.tryAddCurrencyPrice();
      });
  }

  private tryAddCurrencyPrice(): void {
    const currencyCode = this.currencyCodeControl.value;
    const price = this.priceControl.value;

    if (currencyCode && price && price > 0 && this.unusedCurrencies.includes(currencyCode)) {
      this.addCurrencyPrice(currencyCode, price);
    }
  }

  private addCurrencyPrice(currencyCode: string, price: number): void {
    const newPrice: ProductPrice = { currencyCode, price };
    this.currencyPrices.push(newPrice);

    // Create form control for the new price
    const control = new FormControl(price, [Validators.min(0)]);
    this.priceControls.push(control);

    // Setup automatic saving for this price control
    control.valueChanges
      .pipe(takeUntil(this.destroy$), debounceTime(500))
      .subscribe(newPrice => {
        if (newPrice !== null && newPrice >= 0) {
          const index = this.priceControls.indexOf(control);
          if (index >= 0) {
            this.currencyPrices[index].price = newPrice;
            this.emitUpdatedPrices();
          }
        }
      });

    // Clear the input form
    this.currencyCodeControl.setValue('');
    this.priceControl.setValue('');
    this.selectedCurrencySymbol = '';

    this.updateUnusedCurrencies();
    this.emitUpdatedPrices();
  }

  removeCurrencyPrice(index: number): void {
    this.currencyPrices.splice(index, 1);
    this.priceControls.splice(index, 1);
    this.updateUnusedCurrencies();
    this.emitUpdatedPrices();
  }

  getPriceControl(index: number): FormControl {
    return this.priceControls[index] || new FormControl(0);
  }

  displayCurrencyFn = (currencyCode: string): string => {
    if (!currencyCode) return '';
    return `${this.getCurrencySymbol(currencyCode)} ${currencyCode}`;
  }

  private emitUpdatedPrices(): void {
    const prices: { [currencyCode: string]: number } = {};
    this.currencyPrices.forEach(cp => {
      prices[cp.currencyCode] = cp.price;
    });
    this.pricesChanged.emit(prices);
  }

  getCurrencySymbol(code: string): string {
    return this.availableCurrencies[code]?.symbol || code;
  }

  getCurrencyName(code: string): string {
    return this.availableCurrencies[code]?.name || code;
  }

  getCurrencyTranslationKey(code: string): string {
    return this.currencyTranslations[code] || this.availableCurrencies[code]?.translation_key || '';
  }
}
