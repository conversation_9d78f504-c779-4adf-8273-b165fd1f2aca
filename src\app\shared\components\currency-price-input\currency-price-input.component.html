<ng-container *transloco="let t">
  <div class="currency-prices-container">
    <!-- Existing currency prices list -->
    <div class="currency-prices-list" *ngIf="currencyPrices.length > 0">
      <div *ngFor="let currencyPrice of currencyPrices; let i = index" class="currency-price-item">
        <div class="currency-info">
          <span class="currency-symbol">{{ getCurrencySymbol(currencyPrice.currencyCode) }}</span>
          <span class="currency-code">{{ currencyPrice.currencyCode }}</span>
          <div class="price-edit-container">
            <app-fiscomm-input
              [placeholder]="t('price')"
              [control]="getPriceControl(i)"
              type="number"
              [suffix]="getCurrencySymbol(currencyPrice.currencyCode)"
              class="inline-price-input">
            </app-fiscomm-input>
          </div>
        </div>
        <button mat-icon-button color="warn" (click)="removeCurrencyPrice(i)" aria-label="Remove currency" class="remove-button">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>

    <!-- Add new currency section -->
    <div class="add-currency-section" *ngIf="unusedCurrencies.length > 0">
      <div class="add-currency-row">
        <app-fiscomm-input
          [placeholder]="t('currency')"
          [control]="currencyCodeControl"
          [autoComplete]="currencyAuto"
          class="currency-select-input">
        </app-fiscomm-input>

        <mat-autocomplete #currencyAuto="matAutocomplete" [displayWith]="displayCurrencyFn">
          <mat-option *ngFor="let code of filteredCurrencies" [value]="code">
            {{ getCurrencySymbol(code) }} {{ code }} -
            <ng-container *ngIf="getCurrencyTranslationKey(code) && t(getCurrencyTranslationKey(code)); else defaultName">
              {{ t(getCurrencyTranslationKey(code)) }}
            </ng-container>
            <ng-template #defaultName>
              {{ getCurrencyName(code) }}
            </ng-template>
          </mat-option>
        </mat-autocomplete>

        <app-fiscomm-input
          [placeholder]="t('price')"
          [control]="priceControl"
          type="number"
          [suffix]="selectedCurrencySymbol"
          class="price-input">
        </app-fiscomm-input>
      </div>
    </div>

    <!-- Empty state message -->
    <div *ngIf="currencyPrices.length === 0 && unusedCurrencies.length === 0" class="no-currencies-message">
      {{ t('items.no_additional_currencies') }}
    </div>
  </div>
</ng-container>
