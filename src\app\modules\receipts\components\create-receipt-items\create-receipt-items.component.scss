.row {
  background-color: white;

  border-radius: 8px;
  border-bottom: solid 3px #e1e5f2;
}

table {
  table-layout: fixed;
  width: 100%;
  th {
    font-size: 13px;
  }
  td {
    padding-right: 10px;
  }
}
.table > :not(caption) > * > * {
  padding: 0;
}

.button {
  span {
    cursor: pointer;
    font-size: 13px;
  }
}

.icon-col {
  width: 20px;
  .mat-icon {
    font-size: 20px;
    cursor: pointer;
  }
}

.footer {
  border-top: #e1e5f2 solid 1px;
}

@media only screen and (max-width: 992px) {
  h5 {
    font-size: 18px !important;
  }
}

@media only screen and (max-width: 768px) {
  .table tr {
    display: flex;
    flex-direction: column;
  }
  .titleRow {
    display: none !important;
  }
}

@media (max-width: 576px) {
  h5 {
    font-size: 16px !important;
  }
}

.rsd-conversion {
  font-size: 0.85em;
  color: #6c757d;
  margin-top: 2px;
}

// Styling for autocomplete product options

::ng-deep {
  .mat-mdc-option {
    .product-option {
      display: flex;
      flex-direction: column;
      
     width: 100px !important;
      overflow-x:auto;

      .product-name {
        font-weight: normal;
        margin-bottom: 2px;
      }

      .product-details {
        margin-bottom: 4px;

        .text-muted {
          color: #6c757d;
          font-size: 0.85em;
        }
      }

      .product-price {
        color: #2196F3;
        font-weight: 500;
        font-size: 0.9em;
        align-self: flex-end;
        margin-top: auto;
      }
    }
  }
}
