.currency-prices-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 1.5rem;
}

.currency-prices-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.currency-price-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #fafafa;
  min-height: 56px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
  }
}

.currency-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.currency-symbol {
  font-weight: 600;
  color: #044962;
  min-width: 24px;
  font-size: 1.1rem;
}

.currency-code {
  color: #044962a1;
  font-weight: 500;
  font-size: 0.9rem;
  width: 60px;
}

.price-edit-container {
  flex: 1;
  max-width: 200px;
}

.remove-button {
  margin: -8px;
  opacity: 0.6;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
}

.add-currency-section {
  margin-bottom: 16px;
}

.add-currency-row {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 16px 0px 16px;
  border: 1px dashed #d0d0d0;
  border-radius: 4px;

  transition: all 0.2s ease;

  &:hover {
    border-color: #044962a1;
    
  }
}

.currency-select-input {
  flex: 1;
  min-width: 200px;
}

.price-input {
  flex: 1;
  min-width: 150px;
}

.no-currencies-message {
  color: #757575;
  font-style: italic;
  text-align: center;
  padding: 20px;
  border-radius: 4px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  margin-top: 8px;
}

// Inline price input styling
.inline-price-input {
  ::ng-deep {
    .mat-form-field {
      font-size: 12px;
      font-weight: 500;
      width: 100%;
    }

    .mat-form-field-wrapper {
      padding-bottom: 0.5em;
    }

    .mat-form-field-infix {
      padding: 0.5em 0;
    }

    .mat-form-field-label-wrapper {
      top: -0.8em;
    }

    .matLabel {
      color: #044962a1;
    }
  }
}

// Add currency section styling
.currency-select-input,
.price-input {
  ::ng-deep {
    .mat-form-field {
      font-size: 12px;
      font-style: italic;
      font-weight: 500;
      width: 100%;
    }

    .matLabel {
      color: #044962a1;
    }

    .mat-form-field-suffix {
      color: #044962a1;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .add-currency-row {
    flex-direction: column;
    gap: 12px;
  }

  .currency-select-input,
  .price-input {
    min-width: unset;
  }

  .currency-info {
    gap: 12px;
  }

  .price-edit-container {
    max-width: unset;
  }
}
