@import "../../../assets/styles/variables.scss";

// =============================================================================
// DASHBOARD SIDENAV - COMPLETE REWORK
// =============================================================================

// Global container setup
::ng-deep {
  .mat-sidenav-container {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    position: relative;
  }

  .mat-sidenav-content {
    overflow-x: hidden;
    overflow-y: auto;
    height: 100vh;
  }

  .mat-drawer-inner-container {
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  // Form field customizations
  #dashboard {
    .mat-form-field-wrapper {
      padding: 0;
      background-color: white;
      border-radius: 8px;
    }
    .mat-form-field {
      font-size: 0.76rem;
      color: black;
    }
  }

  // Language selector customizations
  #langSelect {
    .mat-form-field {
      width: 85% !important;
    }
    .mat-form-field-outline:hover {
      opacity: 0 !important;
    }
    .mat-select-value {
      color: white;
    }
    .mat-form-field-wrapper {
      background-color: transparent !important;
      &:hover {
        background-color: transparent !important;
      }
    }
    .mat-form-field-outline {
      border-radius: 8px;
      background-color: #e1e5f2;
      opacity: 0.2;
    }
  }
}

// =============================================================================
// SIDENAV STRUCTURE - COMPLETE REWORK
// =============================================================================

// Notification count badge
.count {
  background-color: #e8edff;
  width: 28px;
  height: 20px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

// Main sidenav container
.fiscomm-sidenav {
  background: #094e67;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  transition: width 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  flex-direction: column;

  // Responsive width handling
  &.expanded {
    width: 15em;
  }

  &.collapsed {
    width: 4.5em;
  }
}

// Active state for navigation items
.active {
  background-color: rgba(9, 30, 66, 0.25);
}

// =============================================================================
// NAVIGATION SECTIONS
// =============================================================================

// Scrollable content wrapper
.nav-content-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; // Important for flexbox scrolling

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }
}

// Navigation section containers (no longer scrollable)
.nav-section {
  flex: 1;
  overflow: visible;
}

// Bottom section (positioned at bottom of scrollable area)
.nav-bottom {
  margin-top: auto;
  flex-shrink: 0; // Prevent shrinking
}

// =============================================================================
// NAVIGATION ITEMS
// =============================================================================

// Navigation item wrapper
.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  color: #e1e5f2;
  text-decoration: none;
  border-radius: 0;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &.active {
    background-color: rgba(9, 30, 66, 0.25);

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background-color: #e1e5f2;
    }
  }

  // Collapsed state
  .fiscomm-sidenav.collapsed & {
    justify-content: center;
    padding: 12px 8px;
  }
}

// Icon styling
.nav-icon {
  color: white;
  font-size: 24px;
  width: 24px;
  height: 24px;
  margin-right: 16px;
  flex-shrink: 0;

  .fiscomm-sidenav.collapsed & {
    margin-right: 0;
  }
}

// Text content
.nav-text {
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  transition: opacity 0.2s ease;

  h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 400;
  }

  .fiscomm-sidenav.collapsed & {
    opacity: 0;
    width: 0;
  }
}

// =============================================================================
// DIVIDERS AND SPECIAL ELEMENTS
// =============================================================================

// Section dividers
.nav-divider {
  margin: 16px 16px 8px 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(191, 219, 247, 0.3);

  .divider-text {
    font-size: 12px;
    color: rgba(191, 219, 247, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
    text-align: left;

    .fiscomm-sidenav.collapsed & {
      display: none;
    }
  }

  .fiscomm-sidenav.collapsed & {
    margin: 16px 8px 8px 8px;
    border-bottom: 1px solid rgba(191, 219, 247, 0.3);
  }
}

// Logo section
.nav-logo {
  padding: 0px 26px;
  height: 100px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .logo-icon {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    margin-right: 12px;

    img {
      width: 100%;

      object-fit: contain;
    }
  }

  .logo-text {
    flex: 1;

    img {
      max-width: 100%;
    }

    .fiscomm-sidenav.collapsed & {
      display: none;
    }
  }

  .fiscomm-sidenav.collapsed & {
    justify-content: center;
    padding: 0px 26px;
  }
}

// =============================================================================
// CONTENT AREA
// =============================================================================

.main-content {
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  transition: margin-left 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  flex-direction: column;

  @media (max-width: 800px) {
    margin-left: 0 !important;
    width: 100vw;
    max-width: 100vw;
  }
}

// =============================================================================
// MOBILE NAVIGATION
// =============================================================================

.mobile-nav {
  display: none;
  background-color: #094e67;
  padding: 5px 16px;
  position: sticky;
  top: 0;
  z-index: 999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  @media (max-width: 800px) {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .mobile-menu-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    mat-icon {
      font-size: 24px;
      transform: scale(1.2);
    }
  }
}

// =============================================================================
// UTILITY CLASSES
// =============================================================================

// Active menu state
.activeMenu {
  background-color: #e1e5f2;
}

// Icon styling
mat-icon {
  color: white;
}

// Special color class
.fiscomm-teal-color {
  transform: scale(1.2);
}

// Content wrapper
.content-wrapper {
  flex: 1;
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0;

  @media (max-width: 800px) {
    padding: 0;
  }
}
