<div class="table-container" *ngIf="displayColumns">
  <table
    mat-table
    matSort
    (matSortChange)="sortData($event)"
    *transloco="let t"
    [dataSource]="data"
    multiTemplateDataRows
    cdkDropList
    cdkDropListOrientation="horizontal"
    [cdkDropListData]="displayColumns"
    (cdkDropListDropped)="dropColumn($any($event))"
    class="mat-elevation-z8 receipts-table"
  >
    <ng-container matColumnDef="checkbox">
      <th mat-header-cell *matHeaderCellDef>
        <input type="checkbox" (change)="checkUncheckAll()" />
      </th>
      <td mat-cell *matCellDef="let element">
        <input
          type="checkbox"
          class="checkbox"
          [checked]="getCheckbox(element.invoice_number)"
          (change)="toggleElement(element)"
        />
      </td>
    </ng-container>

    <ng-container matColumnDef="Broj računa">
      <th
        mat-header-cell
        *matHeaderCellDef
        cdkDrag
        [cdkDragData]="'Broj računa'"
      >
        <div class="drag-header-cell">
          {{ t("receipts.invoice_number") }}
          <mat-icon class="drag-handle" cdkDragHandle>drag_indicator</mat-icon>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        <a
          target="_blank"
          href="{{ element.verification_url }}"
          *ngIf="element.verification_url"
          >{{ element.invoice_number }}</a
        >
        <ng-container *ngIf="!element.verification_url">
          {{ element.invoice_number }}
        </ng-container>
      </td>
    </ng-container>
    <ng-container matColumnDef="Broj porudžbine">
      <th
        mat-header-cell
        *matHeaderCellDef
        cdkDrag
        [cdkDragData]="'Broj porudžbine'"
      >
        <div class="drag-header-cell">
          {{ t("receipts.invoice_number_pos") }}
          <mat-icon class="drag-handle" cdkDragHandle>drag_indicator</mat-icon>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.invoice_number_pos }}
      </td>
    </ng-container>
    <ng-container matColumnDef="Tip plaćanja">
      <th
        mat-header-cell
        *matHeaderCellDef
        cdkDrag
        [cdkDragData]="'Tip plaćanja'"
      >
        <div class="drag-header-cell">
          {{ t("receipts.payment") }}
          <mat-icon class="drag-handle" cdkDragHandle>drag_indicator</mat-icon>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        {{
          activeLang == "sr"
            ? (element.payments[0].payment_type.friendly_name | cyrillicToLatin)
            : element.payments[0].payment_type.payment_type
        }}
      </td>
    </ng-container>
    <ng-container matColumnDef="Tip računa">
      <th
        mat-header-cell
        *matHeaderCellDef
        cdkDrag
        [cdkDragData]="'Tip računa'"
      >
        <div class="drag-header-cell">
          {{ t("receipts.invoice_type") }}
          <mat-icon class="drag-handle" cdkDragHandle>drag_indicator</mat-icon>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        {{
          activeLang == "sr"
            ? (element.invoice_type.friendly_name | cyrillicToLatin)
            : element.invoice_type.invoice_type
        }}
      </td>
    </ng-container>
    <ng-container matColumnDef="Tip transakcije">
      <th
        mat-header-cell
        *matHeaderCellDef
        cdkDrag
        [cdkDragData]="'Tip transakcije'"
      >
        <div class="drag-header-cell">
          {{ t("receipts.transaction_type") }}
          <mat-icon class="drag-handle" cdkDragHandle>drag_indicator</mat-icon>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        {{
          activeLang == "sr"
            ? (element.transaction_type.friendly_name | cyrillicToLatin)
            : element.transaction_type.transaction_type
        }}
      </td>
    </ng-container>
    <ng-container matColumnDef="Žurnal">
      <th mat-header-cell *matHeaderCellDef cdkDrag [cdkDragData]="'Žurnal'">
        <div class="drag-header-cell">
          {{ t("receipts.invoice_pdf_url") }}
          <mat-icon class="drag-handle" cdkDragHandle>drag_indicator</mat-icon>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        <a
          target="_blank"
          href="{{ element.invoice_pdf_url }}"
          *ngIf="element.invoice_pdf_url"
          >Link</a
        >
      </td>
    </ng-container>

    <ng-container matColumnDef="Vreme fisk.">
      <th
        mat-header-cell
        *matHeaderCellDef
        cdkDrag
        [cdkDragData]="'Vreme fisk.'"
      >
        <div class="drag-header-cell">
          {{ t("receipts.sdc_date_time") }}
          <mat-icon class="drag-handle" cdkDragHandle>drag_indicator</mat-icon>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.sdc_date_time | fiscommDate }}
      </td>
    </ng-container>
    <ng-container matColumnDef="Ukupno">
      <th mat-header-cell *matHeaderCellDef cdkDrag [cdkDragData]="'Ukupno'">
        <div class="drag-header-cell">
          {{ t("receipts.total_amount") }}
          <mat-icon class="drag-handle" cdkDragHandle>drag_indicator</mat-icon>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.total_amount | rsdCurrency : "RSD " : 2 : "." : "," : 3 }}
      </td>
    </ng-container>
    <ng-container matColumnDef="Kasir">
      <th mat-header-cell *matHeaderCellDef cdkDrag [cdkDragData]="'Kasir'">
        <div class="drag-header-cell">
          {{ t("receipts.cashier") }}
          <mat-icon class="drag-handle" cdkDragHandle>drag_indicator</mat-icon>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.cashier }}
      </td>
    </ng-container>
    <ng-container matColumnDef="Finalizovan">
      <th
        mat-header-cell
        *matHeaderCellDef
        cdkDrag
        [cdkDragData]="'Finalizovan'"
      >
        <div class="drag-header-cell">
          {{ t("receipts.finalized") }}
          <mat-icon class="drag-handle" cdkDragHandle>drag_indicator</mat-icon>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        {{
          element.finalized
            ? t("receipts.finalized")
            : t("receipts.not_finalized")
        }}
      </td>
    </ng-container>

    <ng-container
      *ngFor="let field of metaFields"
      matColumnDef="{{ field.key }}"
    >
      <th mat-header-cell *matHeaderCellDef cdkDrag [cdkDragData]="field.key">
        <div class="drag-header-cell">
          {{ field.name }}
          <mat-icon class="drag-handle" cdkDragHandle>drag_indicator</mat-icon>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ showMetaField(field, element) }}
      </td>
    </ng-container>

    <ng-container matColumnDef="Akcije" stickyEnd>
      <th mat-header-cell *matHeaderCellDef>{{ t("actions") }}</th>
      <td mat-cell *matCellDef="let element">
        <mat-icon [matMenuTriggerFor]="menu">more_vert</mat-icon>
        <mat-menu #menu="matMenu">
          <button
            mat-menu-item
            *ngIf="element.invoice_type.invoice_type == 'Advance'"
            (click)="emitAction({ action: 'Advance', data: element })"
          >
            {{ t("menu.advance") }}
          </button>
          <button
            mat-menu-item
            *ngIf="element.invoice_type.invoice_type == 'Advance'"
            (click)="emitAction({ action: 'Finalize', data: element })"
          >
            {{ t("menu.finalize") }}
          </button>
          <button
            mat-menu-item
            *ngIf="element.transaction_type.transaction_type == 'Sale'"
            (click)="emitAction({ action: 'Refund', data: element })"
          >
            {{ t("menu.refund") }}
          </button>
          <button
            mat-menu-item
            (click)="emitAction({ action: 'copy', data: element })"
          >
            {{ t("menu.copy") }}
          </button>
          <button mat-menu-item (click)="openSendEmailDialog(element)">
            {{ t("menu.send_to_email") }}
          </button>
        </mat-menu>
      </td>
    </ng-container>

    <ng-container matColumnDef="Expand" stickyEnd>
      <th
        mat-header-cell
        *matHeaderCellDef
        aria-label="row actions"
        class="text-center"
      >
        <mat-icon [matMenuTriggerFor]="menu" class="icon">settings</mat-icon>
        <mat-menu #menu="matMenu" xPosition="before">
          <ng-container *ngFor="let col of allColumnsData">
            <div class="column-menu-item">
              <button
                mat-button
                (click)="$event.preventDefault(); toggleColumn(col)"
              >
                <input
                  type="checkbox"
                  [checked]="col.enabled"
                  (click)="$event.stopPropagation(); toggleColumn(col)"
                />

                <label *ngIf="!col.metaField">{{
                  t("receipts." + col.key)
                }}</label>
                <label *ngIf="col.metaField">{{ col.displayName }}</label>
              </button>
            </div>
          </ng-container>
        </mat-menu>
      </th>
      <td mat-cell *matCellDef="let element">
        <button
          class="d-flex justify-content-center"
          mat-icon-button
          aria-label="expand row"
          (click)="
            expandedElement = expandedElement === element ? null : element;
            $event.stopPropagation()
          "
        >
          <mat-icon *ngIf="expandedElement !== element"
            >keyboard_arrow_down</mat-icon
          >
          <mat-icon *ngIf="expandedElement === element"
            >keyboard_arrow_up</mat-icon
          >
        </button>
      </td>
    </ng-container>

    <!-- expandable content -->
    <ng-container matColumnDef="expandedDetail">
      <td
        mat-cell
        *matCellDef="let element"
        [attr.colspan]="displayColumns.length"
      >
        <div
          class="element-detail"
          [@detailExpand]="
            element == expandedElement ? 'expanded' : 'collapsed'
          "
        >
          <app-row-expanded-details
            [items]="element.items"
            [receiptsHistory]="element.advanceChain"
            [historyColumns]="historyColumns"
            [activeLang]="activeLang"
          >
          </app-row-expanded-details>
        </div>
      </td>
    </ng-container>

    <tr
      mat-header-row
      *matHeaderRowDef="displayColumns"
      cdkDropList
      cdkDropListOrientation="horizontal"
      [cdkDropListData]="displayColumns"
      (cdkDropListDropped)="dropColumn($any($event))"
    ></tr>
    <tr
      mat-row
      *matRowDef="let element; columns: displayColumns"
      class="element-row"
      [class.expanded-row]="expandedElement === element"
    ></tr>
    <tr
      mat-row
      *matRowDef="let row; columns: ['expandedDetail']"
      class="detail-row"
    ></tr>
  </table>
</div>
